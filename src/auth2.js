import axios from 'axios';
import databaseName from './myFunctions/databaseName';

// Base Auth Class - contains common authentication logic
class BaseAuth {
  constructor() {
    this.loading = false;
    this.currentUser = null;
  }

  async makeAuthRequest(endpoint, credentials) {
    try {
      const loginData = {
        "Username": credentials.email.toString(),
        "Password": databaseName.GeneratePasswordHash(credentials.password)
      };

      const response = await axios({
        method: 'POST',
        url: endpoint,
        data: loginData,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 500) {
        throw new Error(response.data.message);
      }

      return {
        isOk: true,
        data: response.data.data
      };

    } catch (error) {
      console.error('Auth request error:', error);
      
      let errorMessage = "HTTP Error While Trying To Login";
      if (error.response?.status === 500) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        isOk: false,
        message: errorMessage
      };
    }
  }

  createSessionData(userData, userType) {
    return {
      isOk: true,
      data: userData,
      userType: userType,
      timestamp: new Date().toISOString()
    };
  }

  saveToSession(sessionData) {
    sessionStorage.setItem("KOZ_SERVICE_AUTH", JSON.stringify(sessionData));
  }

  getFromSession() {
    try {
      const sessionData = sessionStorage.getItem("KOZ_SERVICE_AUTH");
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Error parsing session data:', error);
      return null;
    }
  }

  clearSession() {
    sessionStorage.removeItem("KOZ_SERVICE_AUTH");
    this.currentUser = null;
  }
}

// Internal Company Authentication
class InternalAuth extends BaseAuth {
  constructor() {
    super();
    this.userType = 'internal';
    this.endpoint = 'api/Auth/SIMLogin';
    this.defaultUser = {
      sim_first_name: null,
      sim_last_name: null,
      sim_company_name: null,
      sim_office_phone_number: null,
      sim_cell_phone_number: null,
      sim_user_security: null,
      auth_token: null,
      sim_email: null,
      user_type: 'internal'
    };
  }

  async login(credentials) {
    const result = await this.makeAuthRequest(this.endpoint, credentials);
    
    if (result.isOk) {
      const userData = {
        ...this.defaultUser,
        sim_first_name: result.data.sim_first_name,
        sim_last_name: result.data.sim_last_name,
        sim_company_name: result.data.sim_company_name,
        sim_office_phone_number: result.data.sim_office_phone_number,
        sim_cell_phone_number: result.data.sim_cell_phone_number,
        sim_user_security: result.data.sim_user_security,
        auth_token: result.data.auth_token,
        sim_email: result.data.sim_email,
        user_type: 'internal'
      };

      this.currentUser = userData;
      const sessionData = this.createSessionData(userData, this.userType);
      this.saveToSession(sessionData);

      return {
        isOk: true,
        data: userData
      };
    }

    return result;
  }

  restoreFromSession(sessionData) {
    if (sessionData && sessionData.userType === this.userType) {
      this.currentUser = sessionData.data;
      return true;
    }
    return false;
  }
}

// Customer Authentication
class CustomerAuth extends BaseAuth {
  constructor() {
    super();
    this.userType = 'customer';
    this.endpoint = 'api/Auth/CustomerLogin';
    this.defaultUser = {
      customers_first_name: null,
      customers_last_name: null,
      customer_name: null,
      customers_username: null,
      customers_email: null,
      customers_cell_phone_number: null,
      customers_office_phone_number: null,
      auth_token: null,
      user_type: 'customer'
    };
  }

  async login(credentials) {
    const result = await this.makeAuthRequest(this.endpoint, credentials);
    
    if (result.isOk) {
      const userData = {
        ...this.defaultUser,
        customers_first_name: result.data.customers_first_name,
        customers_last_name: result.data.customers_last_name,
        customer_name: result.data.customer_name,
        customers_username: result.data.customers_username,
        customers_email: result.data.customers_email,
        customers_cell_phone_number: result.data.customers_cell_phone_number,
        customers_office_phone_number: result.data.customers_office_phone_number,
        auth_token: result.data.auth_token,
        user_type: 'customer'
      };

      this.currentUser = userData;
      const sessionData = this.createSessionData(userData, this.userType);
      this.saveToSession(sessionData);

      return {
        isOk: true,
        data: userData
      };
    }

    return result;
  }

  restoreFromSession(sessionData) {
    if (sessionData && sessionData.userType === this.userType) {
      this.currentUser = sessionData.data;
      return true;
    }
    return false;
  }
}

// Main Authentication Manager
class AuthManager {
  constructor() {
    this.internalAuth = new InternalAuth();
    this.customerAuth = new CustomerAuth();
    this.currentAuth = null;
    
    // Try to restore session on initialization
    this.restoreSession();
  }

  async logIn(email, password, userType) {
  if (!userType) {
    return {
      isOk: false,
      message: 'Please select a login type'
    };
  }
  
  const credentials = { email, password };
  
  if (userType === 'customer') {
    this.currentAuth = this.customerAuth;
    return await this.customerAuth.login(credentials);
  } else if (userType === 'internal') {
    this.currentAuth = this.internalAuth;
    return await this.internalAuth.login(credentials);
  } else {
    return {
      isOk: false,
      message: 'Invalid login type'
    };
  }
}

  async logOut() {
    if (this.currentAuth) {
      this.currentAuth.clearSession();
    }
    this.currentAuth = null;
  }

  loggedIn() {
    return !!(this.currentAuth && this.currentAuth.currentUser);
  }

  async getUser() {
    if (this.currentAuth && this.currentAuth.currentUser) {
      return {
        isOk: true,
        data: this.currentAuth.currentUser
      };
    }

    // Try to restore from session
    if (this.restoreSession()) {
      return {
        isOk: true,
        data: this.currentAuth.currentUser
      };
    }

    return {
      isOk: false,
      message: 'No user logged in'
    };
  }

  getUserType() {
    return this.currentAuth ? this.currentAuth.userType : null;
  }

  isCustomer() {
    return this.getUserType() === 'customer';
  }

  isInternal() {
    return this.getUserType() === 'internal';
  }

  restoreSession() {
    const sessionData = this.internalAuth.getFromSession();
    
    if (sessionData) {
      if (this.internalAuth.restoreFromSession(sessionData)) {
        this.currentAuth = this.internalAuth;
        return true;
      } else if (this.customerAuth.restoreFromSession(sessionData)) {
        this.currentAuth = this.customerAuth;
        return true;
      }
    }
    
    return false;
  }
}

// Export singleton instance
export default new AuthManager();
