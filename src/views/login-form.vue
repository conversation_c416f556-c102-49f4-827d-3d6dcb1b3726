
<template>
  <form class="login-form" @submit.prevent="onSubmit">
    <dx-form :form-data="formData" :disabled="loading">
      <dx-item
        data-field="userType"
        editor-type="dxRadioGroup"
        :editor-options="{
          stylingMode: 'filled',
          items: userTypeOptions,
          layout: 'horizontal',
          value: 'internal'
        }"
      >
        <dx-required-rule message="Please select a login type" />
        <dx-label text="Login Type" />
      </dx-item>
      <dx-item
        data-field="email"
        editor-type="dxTextBox"
        :editor-options="{ stylingMode: 'filled', placeholder: 'Email or Username' }"
      >
        <dx-required-rule message="Email or Username is required" />
        <dx-label :visible="false" />
      </dx-item>
      <dx-item
        data-field='password'
        editor-type='dxTextBox'
        :editor-options="{ stylingMode: 'filled', placeholder: 'Password', mode: 'password' }"
      >
        <dx-required-rule message="Password is required" />
        <dx-label :visible="false" />
      </dx-item>
      <dx-button-item>
        <dx-button-options
          width="100%"
          type="default"
          template="signInTemplate"
          :use-submit-behavior="true"
        >
        </dx-button-options>
      </dx-button-item>
      <dx-button-item>
        <dx-button-options
          text="I Forgot My Password"
          width="100%"
          styling-mode="outlined"
          :on-click="viewOnlyLogin"
        />
      </dx-button-item>
      <template #signInTemplate>
        <div>
          <span class="dx-button-text">
            <dx-load-indicator v-if="loading" width="24px" height="24px" :visible="true" />
            <span v-if="!loading">Sign In</span>
          </span>
        </div>
      </template>
    </dx-form>
    <DxPopup
      :key="emailSentKey"
      v-model:visible="popupVisible"
      :drag-enabled="false"
      :hide-on-outside-click="false"
      :show-close-button="false"
      :show-title="true"
      height="80%"
      width="80%"
      title="Reset Password"
    >
    <div class="dx-fieldset">
      <div class="dx-field">
        <div class="dx-field-label">Enter Email Address Of Account:</div>
        <div class="dx-field-value">
          <DxTextBox
            v-model:value="resetEmail"
            :disabled="resetEmailSent"
          />
        </div>
      </div>
    </div>
    <DxButton
      :width="250"
      text="Reset Password"
      type="success"
      styling-mode="contained"
      @click="TryResetPassword"
      :disabled="resetEmailSent"
    />
    <div v-if="resetEmailSent == true">
    <p>An Email Has Been Sent To The Above Email Address With An Authentication Code.<br />Please Allow Up To 1 Minute For The Email To Be Sent And Then Enter The Code Below Alongside Your New Password</p>
    <div class="dx-fieldset">
      <div class="dx-field">
        <div class="dx-field-label">Enter New Password:</div>
        <div class="dx-field-value">
          <DxTextBox
            v-model:value="newPassword"
            mode="password"
          />
        </div>
      </div>
      <div class="dx-field">
        <div class="dx-field-label">Re Enter New Password:</div>
        <div class="dx-field-value">
          <DxTextBox
            v-model:value="reNewPassword"
            mode="password"
          />
        </div>
      </div>
      <div class="dx-field">
        <div class="dx-field-label">Authentication Code:</div>
        <div class="dx-field-value">
          <DxTextBox
            v-model:value="authCode"
          />
        </div>
      </div>
    </div>
    <DxButton
      :width="250"
      text="Update Password"
      type="success"
      styling-mode="contained"
      @click="TryChangePassword"
    />
  </div>
    </DxPopup>
  </form>

</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import DxLoadIndicator from "devextreme-vue/load-indicator";
import DxPopup from 'devextreme-vue/popup';
import DxTextBox from 'devextreme-vue/text-box';
import DxButton from 'devextreme-vue/button';
import axios from 'axios';
import DxForm, {
  DxItem,
  DxRequiredRule,
  DxLabel,
  DxButtonItem,
  DxButtonOptions
} from "devextreme-vue/form";
import notify from 'devextreme/ui/notify';

import auth2 from "../auth2";
import databaseName from '@/myFunctions/databaseName';


const formData = ref({
  userType: 'internal'
});
const loading = ref(false);
const router = useRouter();
const route = useRoute();

const userTypeOptions = [
  { text: 'Internal Company', value: 'internal' },
  { text: 'Customer', value: 'customer' }
];

const popupVisible = ref(false);
const resetEmail = ref("");
const resetEmailSent = ref(false);
const emailSentKey = ref(0);

const newPassword = ref("");
const reNewPassword = ref("");
const authCode = ref("");

// Ensure form is properly initialized
onMounted(() => {
  console.log('Form mounted with data:', formData.value);
  // Force set the default value if not already set
  if (!formData.value.userType) {
    formData.value.userType = 'internal';
  }
});

const viewOnlyLogin = () => {
  loading.value = true;
  //auth.defaultLogIn();
  popupVisible.value = true;
};
const onSubmit = async () => {
  const { email, password, userType } = formData.value;

  // Debug logging
  console.log('Form data:', formData.value);
  console.log('User type selected:', userType);

  // Validate that userType is selected
  if (!userType) {
    notify("Please select a login type", "error", 2000);
    return;
  }

  loading.value = true;

  // Use the new auth2 system
  const result = await auth2.logIn(email, password, userType);
  if (!result.isOk) {
    loading.value = false;
    notify(result.message, "error", 2000);
  } else {
    console.log('Login successful with auth2:', result);
    console.log('User type:', auth2.getUserType());
    router.push(route.query.redirect || '/home');
  }
};
const TryChangePassword = async () => {
  if(newPassword.value == "")
  {
    notify("You Must Insert A New Password!", "error", 7000);
  }
  else if(newPassword.value != reNewPassword.value)
  {
    notify("Passwords Do Not Match!", "error", 7000);
  }
  else if(authCode.value == "")
  {
    notify("You Must Enter The Authentication Code From The Email To Change Password", "error", 7000);
  }
  else
  {
    const changePasswordObj = {
      "sim_email": resetEmail.value,
      "new_password_hash": databaseName.GeneratePasswordHash(newPassword.value),
      "auth_code": authCode.value
    };

    await axios({
      method: 'PATCH',
      url: 'api/Auth/UpdatePassword',
      data: changePasswordObj,
      header:{
        'Content-Type': 'application/json'
      }
    }).then(resp=>{
      console.log(resp);
      notify(resp.data.message, "success", 7000);
      popupVisible.value = false;
      loading.value = false;
    }).catch(exception=>{
      console.log(exception);

      if(exception.response.status == 500)
      {
        notify(exception.response.data.message, "error", 10000);
      }
      else
      {
        notify("HTTP Error While Trying To Login", "error", 10000);
      }
    })
  }
};
const TryResetPassword = async () => {
  console.log(resetEmail.value);
  if(resetEmail.value != "")
  {
    await axios({
      method: 'POST',
      url: `api/Auth/RequestResetPassword?Email=${resetEmail.value}`
    }).then(resp => {
      console.log(resp);

      notify(resp.data.message, "success", 10000);
      resetEmailSent.value = true;
      emailSentKey.value++;

    }).catch(exception => {
      resetEmailSent.value = false;
      console.log(exception);

      if(exception.response.status == 500)
      {
        notify(exception.response.data.message, "error", 10000);
      }
      else
      {
        notify("HTTP Error While Trying To Login", "error", 10000);
      }
    })
  }
  else
  {
    resetEmailSent.value = false;
    notify("You Must Input An Email Address Before Submitting!", "error", 5000);
  }
};
</script>

<style lang="scss">
@import "../themes/generated/variables.base.scss";

.login-form {
  flex: auto;
  .link {
    text-align: center;
    font-size: 16px;
    font-style: normal;

    a {
      text-decoration: none;
    }
  }

  .form-text {
    margin: 10px 0;
    color: rgba($base-text-color, alpha($base-text-color) * 0.7);
  }
}
</style>
